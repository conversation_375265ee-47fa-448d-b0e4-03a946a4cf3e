import { test, expect } from './fixtures/base-fixture';
import { TestConfig } from './config/test-config';

test.describe('New Password Page', () => {
  test.beforeEach(async ({ page, forgetPasswordPage }) => {
    // First go to the forget-password page
    await forgetPasswordPage.goto();

    // Manually set the hasResetToken flag in localStorage to bypass the guard
    await page.evaluate(() => {
      localStorage.setItem('hasResetToken', 'true');
    });

    // Now navigate directly to the new-password page
    await page.goto('/auth/new-password');

    // Now we should be on the new password page
    await expect(page).toHaveURL(/\/new-password/);
  });

  test('new password page should load correctly', async ({
    newPasswordPage,
  }) => {
    // Get the visibility status of all UI elements
    const visibility = await newPasswordPage.getUIElementsVisibility();

    // Verify we're on the new password page
    expect(visibility.isOnNewPasswordPage).toBeTruthy();

    // Verify form elements are visible
    expect(visibility.formElements.resetTokenInputVisible).toBeTruthy();
    expect(visibility.formElements.passwordInputVisible).toBeTruthy();
    expect(visibility.formElements.confirmPasswordInputVisible).toBeTruthy();
    expect(visibility.formElements.saveButtonVisible).toBeTruthy();

    // Verify navigation elements are visible
    expect(visibility.navigationElements.signUpLinkVisible).toBeTruthy();

    // Verify UI elements are visible
    expect(visibility.uiElements.welcomeTextVisible).toBeTruthy();
    expect(visibility.uiElements.welcomeImageVisible).toBeTruthy();
    expect(visibility.uiElements.logoImageVisible).toBeTruthy();
    expect(visibility.uiElements.newPasswordTitleVisible).toBeTruthy();
  });

  test.describe('Form Validation', () => {
    test('should disable save button for empty or invalid inputs', async ({
      newPasswordPage,
    }) => {
      // Initially button should be disabled
      expect(await newPasswordPage.isSaveButtonDisabled()).toBeTruthy();

      // Fill only reset token
      await newPasswordPage.resetTokenInput.fill(
        TestConfig.newPassword.valid.resetToken,
      );
      expect(await newPasswordPage.isSaveButtonDisabled()).toBeTruthy();

      // Fill only password
      await newPasswordPage.resetTokenInput.clear();
      await newPasswordPage.passwordInput.fill(
        TestConfig.newPassword.valid.password,
      );
      expect(await newPasswordPage.isSaveButtonDisabled()).toBeTruthy();

      // Fill only confirm password
      await newPasswordPage.passwordInput.clear();
      await newPasswordPage.confirmPasswordInput.fill(
        TestConfig.newPassword.valid.confirmPassword,
      );
      expect(await newPasswordPage.isSaveButtonDisabled()).toBeTruthy();

      // Fill all fields with valid values
      await newPasswordPage.resetTokenInput.fill(
        TestConfig.newPassword.valid.resetToken,
      );
      await newPasswordPage.passwordInput.fill(
        TestConfig.newPassword.valid.password,
      );
      await newPasswordPage.confirmPasswordInput.fill(
        TestConfig.newPassword.valid.confirmPassword,
      );
      expect(await newPasswordPage.isSaveButtonDisabled()).toBeFalsy();
    });

    test('should show validation errors for invalid password formats and mismatched passwords', async ({
      newPasswordPage,
    }) => {
      const { password, confirmPassword } = TestConfig.newPassword.malformed;

      // Test invalid password (too short)
      await newPasswordPage.resetTokenInput.fill(
        TestConfig.newPassword.valid.resetToken,
      );
      await newPasswordPage.passwordInput.fill(password);
      await newPasswordPage.passwordInput.blur();

      // Test password mismatch
      await newPasswordPage.passwordInput.fill(
        TestConfig.newPassword.valid.password,
      );
      await newPasswordPage.confirmPasswordInput.fill(confirmPassword);
      await newPasswordPage.confirmPasswordInput.blur();
      expect(await newPasswordPage.isPasswordMatchErrorVisible()).toBeTruthy();

      // Button should be disabled with invalid inputs
      expect(await newPasswordPage.isSaveButtonDisabled()).toBeTruthy();
    });
  });

  test.describe('Password Reset Process', () => {
    test('should reset password with valid token and matching passwords', async ({
      newPasswordPage,
      page,
    }) => {
      // Mock the API response for successful password reset
      await page.route('**/users/reset-password/', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            message: TestConfig.messages.resetPasswordSuccess,
          }),
        });
      });

      // Use the setNewPassword method and get the result
      const resetResult = await newPasswordPage.setNewPassword(
        TestConfig.newPassword.valid.resetToken,
        TestConfig.newPassword.valid.password,
        TestConfig.newPassword.valid.confirmPassword,
      );

      // Verify we're redirected to login page
      await expect(page).toHaveURL(/\/login/);

      // Check for success toast
      if (resetResult.hasSuccessToast && resetResult.successToastText) {
        expect(resetResult.successToastText).toContain(
          TestConfig.messages.resetPasswordSuccess,
        );
      }
    });

    test('should show error for invalid reset token', async ({
      newPasswordPage,
      page,
    }) => {
      // Mock the API response for invalid reset token
      await page.route('**/users/reset-password/', async route => {
        await route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({
            error: TestConfig.messages.invalidResetToken,
          }),
        });
      });

      // Use the setNewPassword method and get the result
      const resetResult = await newPasswordPage.setNewPassword(
        TestConfig.newPassword.invalid.resetToken,
        TestConfig.newPassword.valid.password,
        TestConfig.newPassword.valid.confirmPassword,
      );

      // Verify we're still on the new password page
      await expect(page).toHaveURL(/\/new-password/);

      // Check for error toast
      if (resetResult.hasErrorToast && resetResult.errorToastText) {
        expect(resetResult.errorToastText).toContain(
          TestConfig.messages.invalidResetToken,
        );
      }
    });
  });

  test.describe('Navigation', () => {
    test('should navigate to signup page', async ({
      newPasswordPage,
      page,
    }) => {
      await newPasswordPage.goToSignUp();
      await expect(page).toHaveURL(/\/register/);
    });
  });

  test.describe('Security Tests', () => {
    test('should handle XSS attempts in input fields', async ({
      newPasswordPage,
    }) => {
      // Try to inject XSS payload
      await newPasswordPage.resetTokenInput.fill(
        TestConfig.securityPayloads.xss,
      );
      await newPasswordPage.passwordInput.fill(TestConfig.securityPayloads.xss);
      await newPasswordPage.confirmPasswordInput.fill(
        TestConfig.securityPayloads.xss,
      );

      // The form should sanitize inputs or at least not execute scripts
      // Should not crash the application
      await expect(newPasswordPage.resetTokenInput).toBeVisible();
    });

    test('should handle SQL injection attempts', async ({
      newPasswordPage,
    }) => {
      // Try SQL injection in reset token field
      await newPasswordPage.resetTokenInput.fill(
        TestConfig.securityPayloads.sqlInjection,
      );
      await newPasswordPage.passwordInput.fill(
        TestConfig.newPassword.valid.password,
      );
      await newPasswordPage.confirmPasswordInput.fill(
        TestConfig.newPassword.valid.confirmPassword,
      );

      // Submit the form
      if (!(await newPasswordPage.isSaveButtonDisabled())) {
        await newPasswordPage.clickSave();

        // Should not allow reset with SQL injection
        // We're just checking that the application doesn't crash
        await expect(newPasswordPage.resetTokenInput).toBeVisible();
      }
    });

    test('should handle extremely long inputs', async ({ newPasswordPage }) => {
      // Try very long inputs to test against buffer overflow
      const longInput = TestConfig.securityPayloads.longInput.substring(0, 50);

      await newPasswordPage.resetTokenInput.fill(longInput);
      await newPasswordPage.passwordInput.fill(
        TestConfig.newPassword.valid.password,
      );
      await newPasswordPage.confirmPasswordInput.fill(
        TestConfig.newPassword.valid.confirmPassword,
      );

      // Form should still be functional after attempt
      await expect(newPasswordPage.resetTokenInput).toBeVisible();
    });
  });
});
