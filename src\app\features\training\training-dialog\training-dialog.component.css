.dialog-wrapper {
  padding: 24px;
}

.form-input {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  line-height: 1.5;
}

::ng-deep .mat-mdc-dialog-container {
  width: 860px;
  height: 580px;
}

::ng-deep .mat-horizontal-content-container {
  padding: 0px !important;
}

::ng-deep .mat-mdc-dialog-content {
  padding: 0px !important;
}

::ng-deep .mat-mdc-select-panel {
  margin-left: -10px;
}

.select-menu {
  background-color: white;
  height: 48px;
  border: 1px solid #ccc;
  border-radius: 8px;
}

.range {
  width: 120px !important;
}

.form-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.flex-grow {
  flex-grow: 1;
}

.dialog-actions {
  position: absolute;
  bottom: 25px;
}

::ng-deep .mat-horizontal-stepper-header {
  display: none !important;
}

::ng-deep .mat-stepper-horizontal-line {
  display: none;
}
