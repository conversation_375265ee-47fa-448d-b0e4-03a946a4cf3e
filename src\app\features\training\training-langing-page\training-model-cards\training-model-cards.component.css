.card-wrapper {
  background-color: white;
  border: 1px;
  border-radius: 12px;
  margin-bottom: 5px;
}
.status {
  color: #73777f;
}
.completed {
  padding: 12px 20px;
  cursor: pointer;
}
.configured {
  background-color: #e8def8;
  padding: 12px 20px;
  border-radius: 15px;
  cursor: pointer;
}
.stopped {
  background-color: #fdd3d0;
  padding: 12px 20px;
  border-radius: 15px;
  cursor: pointer;
}
.training {
  background: linear-gradient(to right, #e8def8, #ffffff);
  padding: 12px 20px;
  border-radius: 15px;
  cursor: pointer;
}

.failed {
  background-color: #fdd3d0;
  padding: 12px 20px;
  border-radius: 15px;
  cursor: pointer;
}
.color_gray {
  color: rgb(171, 169, 169);
}
