/**
 * Test configuration for E2E tests
 */
export const TestConfig = {
  // baseUrl: 'http://platform.dev.aicuflow.com',
  baseUrl: 'http://localhost:4200',

  // Test users
  users: {
    valid: {
      email: '<EMAIL>',
      password: 'palo786!@#',
    },
    invalid: {
      email: '<EMAIL>',
      password: 'invalidPassword123',
    },
    malformed: {
      email: 'not-an-email',
      password: 'short',
    },
  },

  // Test signup data
  signup: {
    valid: {
      firstName: 'Test',
      lastName: 'User',
      email: 'test.user' + Date.now() + '@example.com', // Unique email
      password: 'Password123!',
      confirmPassword: 'Password123!',
    },
    invalid: {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>', // Already registered email
      password: 'Password123!',
      confirmPassword: 'Password123!',
    },
    malformed: {
      firstName: '',
      lastName: '',
      email: 'not-an-email',
      password: 'short',
      confirmPassword: 'different',
    },
  },

  // Test forget password data
  forgetPassword: {
    valid: {
      email: '<EMAIL>',
    },
    invalid: {
      email: '<EMAIL>',
    },
    malformed: {
      email: 'not-an-email',
    },
  },

  // Test new password data
  newPassword: {
    valid: {
      resetToken: 'validResetToken123',
      password: 'NewPassword123!',
      confirmPassword: 'NewPassword123!',
    },
    invalid: {
      resetToken: 'invalidResetToken',
      password: 'NewPassword123!',
      confirmPassword: 'NewPassword123!',
    },
    malformed: {
      resetToken: '',
      password: 'short',
      confirmPassword: 'different',
    },
  },

  // Test timeouts
  timeouts: {
    navigation: 10000,
    animation: 1000,
    network: 5000,
  },

  // Expected messages
  messages: {
    loginSuccess: 'Login successful!',
    signupSuccess: 'Registration successful!',
    invalidCredentials: 'Invalid credentials',
    emailValidation: 'Please enter a valid email address',
    passwordValidation: 'Minimum 8 characters are required',
    passwordMatchValidation: 'Passwords do not match',
    duplicateEmail: 'Email already exists',
    resetEmailSuccess: 'Email has been sent successfully',
    resetPasswordSuccess: 'Password reset successfully',
    userNotFound: 'User matching query does not exist.',
    invalidResetToken: 'Invalid reset token',
  },

  // Security test payloads
  securityPayloads: {
    xss: '<script>alert("XSS")</script>',
    sqlInjection: "' OR 1=1 --",
    longInput: 'a'.repeat(1000),
  },

  // Performance thresholds (in milliseconds)
  performance: {
    loginResponseTime: 3000,
    signupResponseTime: 5000, // Increased to account for network latency
    forgetPasswordResponseTime: 3000,
    newPasswordResponseTime: 3000,
  },
};
