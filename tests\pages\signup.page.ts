import { Page, Locator } from '@playwright/test';

export class SignupPage {
  // Locators
  readonly firstNameInput: Locator;
  readonly lastNameInput: Locator;
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly confirmPasswordInput: Locator;
  readonly signupButton: Locator;
  readonly loginLink: Locator;
  readonly welcomeText: Locator;
  readonly welcomeImage: Locator;
  readonly logoImage: Locator;
  readonly emailErrorMessage: Locator;
  readonly passwordErrorMessage: Locator;
  readonly passwordMatchErrorMessage: Locator;
  readonly successToast: Locator;
  readonly errorToast: Locator;
  readonly passwordVisibilityToggle: Locator;
  readonly confirmPasswordVisibilityToggle: Locator;

  constructor(private page: Page) {
    this.firstNameInput = page.locator('input[formControlName="first_name"]');
    this.lastNameInput = page.locator('input[formControlName="last_name"]');
    this.emailInput = page.locator('input[formControlName="email"]');
    this.passwordInput = page.locator('input[formControlName="password1"]');
    this.confirmPasswordInput = page.locator(
      'input[formControlName="password2"]',
    );
    this.signupButton = page.locator('button', { hasText: 'Sign Up' });
    this.loginLink = page.locator('a', { hasText: 'Login' });
    this.welcomeText = page.locator('h1.welcome', { hasText: 'Welcome' });
    this.welcomeImage = page.locator('img[src*="assets/Welcome.png"]');
    this.logoImage = page.locator('img[src*="assets/Logo.png"]');
    this.emailErrorMessage = page.locator(
      'text=Please enter a valid email address',
    );
    this.passwordErrorMessage = page.locator(
      'text=Minimum 8 characters are required',
    );
    this.passwordMatchErrorMessage = page.locator(
      'text=Passwords do not match',
    );
    this.successToast = page.locator('.toast-success');
    this.errorToast = page.locator('.toast-error');
    this.passwordVisibilityToggle = page
      .locator('button:has(mat-icon)')
      .first();
    this.confirmPasswordVisibilityToggle = page
      .locator('button:has(mat-icon)')
      .last();
  }

  /**
   * Navigate to the signup page
   */
  async goto() {
    try {
      // Navigate to the signup page with a longer timeout
      await this.page.goto('/auth/register', { timeout: 30000 });

      // Wait for the page to be fully loaded
      await this.page.waitForLoadState('domcontentloaded', { timeout: 30000 });
      await this.page.waitForLoadState('networkidle', { timeout: 30000 });

      // Wait a bit more to ensure everything is loaded
      await this.page.waitForTimeout(1000);
    } catch (e) {
      // Error navigating to signup page
      throw e;
    }
  }

  /**
   * Fill the signup form with all required fields
   */
  async fillSignupForm(
    firstName: string,
    lastName: string,
    email: string,
    password: string,
    confirmPassword: string,
  ) {
    await this.firstNameInput.fill(firstName);
    await this.lastNameInput.fill(lastName);
    await this.emailInput.fill(email);
    await this.passwordInput.fill(password);
    await this.confirmPasswordInput.fill(confirmPassword);
  }

  /**
   * Click the signup button
   */
  async clickSignup() {
    try {
      // Click the signup button with a longer timeout
      await this.signupButton.click({ timeout: 10000 });

      // Wait for navigation or response
      await this.page.waitForTimeout(2000); // Wait a bit for any response

      // Wait for any network requests to complete
      await this.page.waitForLoadState('networkidle', { timeout: 10000 });
    } catch (e) {
      // Error clicking signup button
      throw e; // Re-throw to allow test to fail
    }
  }

  /**
   * Complete the signup process with provided information
   * Returns the current state without assertions
   */
  async signup(
    firstName: string,
    lastName: string,
    email: string,
    password: string,
    confirmPassword: string,
  ) {
    try {
      // Fill the form
      await this.fillSignupForm(
        firstName,
        lastName,
        email,
        password,
        confirmPassword,
      );

      // Click signup
      await this.clickSignup();

      // Return current state without assertions
      return {
        url: this.page.url(),
        hasSuccessToast: await this.successToast.isVisible(),
        hasErrorToast: await this.errorToast.isVisible(),
        successToastText: await this.getSuccessToastText(),
        errorToastText: await this.getErrorToastText(),
      };
    } catch (e) {
      // Error during signup process
      return {
        url: this.page.url(),
        hasSuccessToast: false,
        hasErrorToast: false,
        successToastText: null,
        errorToastText: null,
        error: e,
      };
    }
  }

  /**
   * Navigate to the login page
   */
  async goToLogin() {
    await this.loginLink.click();
    await this.page.waitForURL(/\/login/, { timeout: 10000 });
  }

  /**
   * Check if signup button is disabled
   */
  async isSignupButtonDisabled() {
    return this.signupButton.isDisabled();
  }

  /**
   * Get the current URL
   */
  async getCurrentUrl() {
    return this.page.url();
  }

  /**
   * Toggle password visibility
   */
  async togglePasswordVisibility() {
    await this.passwordVisibilityToggle.click();
  }

  /**
   * Toggle confirm password visibility
   */
  async toggleConfirmPasswordVisibility() {
    await this.confirmPasswordVisibilityToggle.click();
  }

  /**
   * Check if email validation error is visible
   */
  async isEmailErrorVisible() {
    try {
      return await this.emailErrorMessage.isVisible();
    } catch {
      return false;
    }
  }

  /**
   * Check if password validation error is visible
   */
  async isPasswordErrorVisible() {
    try {
      return await this.passwordErrorMessage.isVisible();
    } catch {
      return false;
    }
  }

  /**
   * Check if password match error is visible
   */
  async isPasswordMatchErrorVisible() {
    try {
      return await this.passwordMatchErrorMessage.isVisible();
    } catch {
      return false;
    }
  }

  /**
   * Get text from success toast if visible
   */
  async getSuccessToastText() {
    try {
      const successToasts = this.page.locator('.toast-success');
      const count = await successToasts.count();

      if (count > 0) {
        // Get text from the first success toast
        return await successToasts.first().textContent();
      }
    } catch (e) {
      // Error getting success toast text
    }
    return null;
  }

  /**
   * Get text from error toast if visible
   */
  async getErrorToastText() {
    try {
      const errorToasts = this.page.locator('.toast-error');
      const count = await errorToasts.count();

      if (count > 0) {
        // Get text from the first error toast
        return await errorToasts.first().textContent();
      }
    } catch (e) {
      // Error getting error toast text
    }
    return null;
  }

  /**
   * Get visibility status of UI elements on the signup page
   * @returns Object containing visibility status of all UI elements
   */
  async getUIElementsVisibility() {
    try {
      // Use longer timeouts for visibility checks
      const timeout = { timeout: 10000 };

      // Collect the visibility status of all elements
      const currentUrl = this.page.url();
      const isOnSignupPage = /\/register/i.test(currentUrl);

      return {
        currentUrl,
        isOnSignupPage,
        formElements: {
          firstNameInputVisible: await this.firstNameInput.isVisible(timeout),
          lastNameInputVisible: await this.lastNameInput.isVisible(timeout),
          emailInputVisible: await this.emailInput.isVisible(timeout),
          passwordInputVisible: await this.passwordInput.isVisible(timeout),
          confirmPasswordInputVisible:
            await this.confirmPasswordInput.isVisible(timeout),
          signupButtonVisible: await this.signupButton.isVisible(timeout),
        },
        navigationElements: {
          loginLinkVisible: await this.loginLink.isVisible(timeout),
        },
        uiElements: {
          welcomeTextVisible: await this.welcomeText.isVisible(timeout),
          welcomeImageVisible: await this.welcomeImage.isVisible(timeout),
          logoImageVisible: await this.logoImage.isVisible(timeout),
        },
      };
    } catch (e: unknown) {
      // Error checking UI elements visibility
      const errorMessage = e instanceof Error ? e.message : String(e);

      return {
        error: errorMessage,
        currentUrl: 'unknown',
        isOnSignupPage: false,
        formElements: {
          firstNameInputVisible: false,
          lastNameInputVisible: false,
          emailInputVisible: false,
          passwordInputVisible: false,
          confirmPasswordInputVisible: false,
          signupButtonVisible: false,
        },
        navigationElements: { loginLinkVisible: false },
        uiElements: {
          welcomeTextVisible: false,
          welcomeImageVisible: false,
          logoImageVisible: false,
        },
      };
    }
  }
}
