import { test, expect } from './fixtures/base-fixture';
import { TestConfig } from './config/test-config';

test.describe('Login Page', () => {
  test.beforeEach(async ({ loginPage }) => {
    // Navigate to login page
    await loginPage.goto();
  });

  test('login page should load correctly', async ({ loginPage, page }) => {
    // Get the visibility status of all UI elements
    const visibility = await loginPage.getUIElementsVisibility();

    // Verify we're on the login page
    expect(visibility.isOnLoginPage).toBeTruthy();

    // Verify form elements are visible
    expect(visibility.formElements.emailInputVisible).toBeTruthy();
    expect(visibility.formElements.passwordInputVisible).toBeTruthy();
    expect(visibility.formElements.loginButtonVisible).toBeTruthy();

    // Verify navigation elements are visible
    expect(visibility.navigationElements.signUpLinkVisible).toBeTruthy();
    expect(
      visibility.navigationElements.forgotPasswordLinkVisible,
    ).toBeTruthy();

    // Verify UI elements are visible
    expect(visibility.uiElements.welcomeTextVisible).toBeTruthy();
    expect(visibility.uiElements.welcomeImageVisible).toBeTruthy();
    expect(visibility.uiElements.logoImageVisible).toBeTruthy();
  });

  test.describe('Authentication', () => {
    test('should login successfully with valid credentials', async ({
      loginPage,
      page,
    }) => {
      const { email, password } = TestConfig.users.valid;

      // Get login result
      const loginResult = await loginPage.login(email, password);

      // Verify login was successful
      await expect(page).toHaveURL(/\/dashboard\/projects$/);

      // Optional: Check for success toast if it appears
      if (loginResult.hasSuccessToast) {
        const successToastText = await loginPage.getSuccessToastText();
        if (successToastText) {
          expect(successToastText).toContain(TestConfig.messages.loginSuccess);
        }
      }
    });

    test('should show error for invalid credentials', async ({
      loginPage,
      page,
    }) => {
      const { email, password } = TestConfig.users.invalid;

      // Fill in the form manually instead of using login method
      await loginPage.fillLoginForm(email, password);

      // Click login button
      await loginPage.loginButton.click();

      // Wait for any response
      await page.waitForTimeout(2000);

      // Verify we're still on the login page (this is the main assertion for invalid credentials)
      await expect(page).toHaveURL(/\/login|\/auth\/login/);
    });

    test('should prevent navigation to dashboard without authentication', async ({
      loginPage,
      page,
      context,
    }) => {
      // Try to navigate directly to dashboard
      await page.goto('/dashboard/projects');

      // Should be redirected to login
      await expect(page).toHaveURL(/\/login/);

      // Login successfully
      const { email, password } = TestConfig.users.valid;
      await loginPage.login(email, password);

      // Should now be on dashboard
      await expect(page).toHaveURL(/\/dashboard\/projects$/);

      // Open a new page and try to access dashboard
      const newPage = await context.newPage();
      await newPage.goto(`${TestConfig.baseUrl}/dashboard/projects`);

      // Should be on dashboard because we're authenticated
      await expect(newPage).toHaveURL(/\/dashboard\/projects$/);

      // Clear storage
      await newPage.evaluate(() => {
        localStorage.clear();
        sessionStorage.clear();
      });

      // Reload and should be redirected to login
      await newPage.reload();
      await expect(newPage).toHaveURL(/\/login/);

      await newPage.close();
    });
  });

  test.describe('Form Validation', () => {
    test('should disable login button for empty or invalid inputs', async ({
      loginPage,
    }) => {
      // Initially button should be disabled
      expect(await loginPage.isLoginButtonDisabled()).toBeTruthy();

      // Fill only email
      await loginPage.emailInput.fill(TestConfig.users.valid.email);
      expect(await loginPage.isLoginButtonDisabled()).toBeTruthy();

      // Fill only password
      await loginPage.emailInput.clear();
      await loginPage.passwordInput.fill(TestConfig.users.valid.password);
      expect(await loginPage.isLoginButtonDisabled()).toBeTruthy();

      // Fill both with valid values
      await loginPage.emailInput.fill(TestConfig.users.valid.email);
      expect(await loginPage.isLoginButtonDisabled()).toBeFalsy();
    });

    test('should show validation errors for invalid email and password formats', async ({
      loginPage,
    }) => {
      // Test invalid email format
      await loginPage.emailInput.fill(TestConfig.users.malformed.email);
      await loginPage.emailInput.blur();
      expect(await loginPage.isEmailErrorVisible()).toBeTruthy();

      // Test invalid password (too short)
      await loginPage.passwordInput.fill(TestConfig.users.malformed.password);
      await loginPage.passwordInput.blur();
      expect(await loginPage.isPasswordErrorVisible()).toBeTruthy();

      // Button should be disabled with invalid inputs
      expect(await loginPage.isLoginButtonDisabled()).toBeTruthy();
    });
  });

  test.describe('Navigation', () => {
    test('should navigate to signup page and back', async ({
      loginPage,
      page,
    }) => {
      await loginPage.goToSignUp();
      await expect(page).toHaveURL(/\/register/);

      // Navigate back to login
      await page.goBack();
      await expect(page).toHaveURL(/\/login/);
    });

    test('should navigate to forgot password page', async ({
      loginPage,
      page,
    }) => {
      await loginPage.goToForgotPassword();
      await expect(page).toHaveURL(/\/forget-password/);
    });
  });

  test.describe('Security Tests', () => {
    test('should handle XSS attempts in input fields', async ({
      loginPage,
    }) => {
      // Try to inject XSS payload
      await loginPage.emailInput.fill(TestConfig.securityPayloads.xss);
      await loginPage.passwordInput.fill(TestConfig.securityPayloads.xss);

      // The form should sanitize inputs or at least not execute scripts
      // For XSS test, we just verify the app didn't crash and inputs were sanitized
      // Should not crash the application
      await expect(loginPage.emailInput).toBeVisible();
    });

    test('should handle SQL injection attempts', async ({
      loginPage,
      page,
    }) => {
      // Try SQL injection in login form
      await loginPage.emailInput.fill(TestConfig.users.valid.email);
      await loginPage.passwordInput.fill(
        TestConfig.securityPayloads.sqlInjection,
      );

      // Check if the button is enabled (it might be since we have a valid email)
      const isDisabled = await loginPage.isLoginButtonDisabled();

      // If button is enabled, try to click it
      if (!isDisabled) {
        await loginPage.loginButton.click();
        await page.waitForTimeout(2000);

        // Should not allow login with SQL injection
        const isOnDashboard = await loginPage.isOnDashboard();
        expect(isOnDashboard).toBeFalsy();
      }
    });

    test('should handle extremely long inputs', async ({ loginPage }) => {
      // Try very long inputs to test against buffer overflow
      await loginPage.emailInput.fill(
        `long-email-${TestConfig.securityPayloads.longInput.substring(0, 50)}@example.com`,
      );
      await loginPage.passwordInput.fill(
        TestConfig.securityPayloads.longInput.substring(0, 100),
      );

      // Form should still be functional after attempt
      await expect(loginPage.emailInput).toBeVisible();
    });
  });

  test.describe('Performance Tests', () => {
    test('login response time should be within acceptable threshold', async ({
      loginPage,
      page,
    }) => {
      const { email, password } = TestConfig.users.valid;

      // Measure login response time
      const startTime = Date.now();
      await loginPage.login(email, password);
      const endTime = Date.now();

      const responseTime = endTime - startTime;

      // Verify response time is within acceptable threshold
      expect(responseTime).toBeLessThan(
        TestConfig.performance.loginResponseTime,
      );

      // Verify login was successful
      await expect(page).toHaveURL(/\/dashboard\/projects$/);
    });
  });
});
