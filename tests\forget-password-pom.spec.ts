import { test, expect } from './fixtures/base-fixture';
import { TestConfig } from './config/test-config';

test.describe('Forget Password Page', () => {
  test.beforeEach(async ({ forgetPasswordPage }) => {
    await forgetPasswordPage.goto();
  });

  test('forget password page should load correctly', async ({
    forgetPasswordPage,
  }) => {
    // Get the visibility status of all UI elements
    const visibility = await forgetPasswordPage.getUIElementsVisibility();

    // Verify we're on the forget password page
    expect(visibility.isOnForgetPasswordPage).toBeTruthy();

    // Verify form elements are visible
    expect(visibility.formElements.emailInputVisible).toBeTruthy();
    expect(visibility.formElements.resetButtonVisible).toBeTruthy();

    // Verify navigation elements are visible
    expect(visibility.navigationElements.signUpLinkVisible).toBeTruthy();

    // Verify UI elements are visible
    expect(visibility.uiElements.welcomeTextVisible).toBeTruthy();
    expect(visibility.uiElements.welcomeImageVisible).toBeTruthy();
    expect(visibility.uiElements.logoImageVisible).toBeTruthy();
    expect(visibility.uiElements.forgotPasswordTitleVisible).toBeTruthy();
  });

  test.describe('Form Validation', () => {
    test('should disable reset button for empty or invalid email', async ({
      forgetPasswordPage,
    }) => {
      // Initially button should be disabled
      expect(await forgetPasswordPage.isResetButtonDisabled()).toBeTruthy();

      // Fill with invalid email
      await forgetPasswordPage.fillEmail(
        TestConfig.forgetPassword.malformed.email,
      );
      expect(await forgetPasswordPage.isResetButtonDisabled()).toBeTruthy();

      // Fill with valid email
      await forgetPasswordPage.fillEmail(TestConfig.forgetPassword.valid.email);
      expect(await forgetPasswordPage.isResetButtonDisabled()).toBeFalsy();
    });

    test('should show validation error for invalid email format', async ({
      forgetPasswordPage,
    }) => {
      // Test invalid email format
      await forgetPasswordPage.fillEmail(
        TestConfig.forgetPassword.malformed.email,
      );
      await forgetPasswordPage.emailInput.blur();
      expect(await forgetPasswordPage.isEmailErrorVisible()).toBeTruthy();

      // Button should be disabled with invalid input
      expect(await forgetPasswordPage.isResetButtonDisabled()).toBeTruthy();
    });
  });

  test.describe('Password Reset Process', () => {
    test('should send reset email for valid user', async ({
      forgetPasswordPage,
      page,
    }) => {
      // Mock the API response for successful reset
      await page.route('**/users/forgot-password/', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            message: TestConfig.messages.resetEmailSuccess,
          }),
        });
      });

      // Use the resetPassword method and get the result
      const resetResult = await forgetPasswordPage.resetPassword(
        TestConfig.forgetPassword.valid.email,
      );

      // Verify we're redirected to new password page
      await expect(page).toHaveURL(/\/new-password/);

      // Check for success toast
      if (resetResult.hasSuccessToast && resetResult.successToastText) {
        expect(resetResult.successToastText).toContain(
          TestConfig.messages.resetEmailSuccess,
        );
      }
    });

    test('should show error for non-existent user', async ({
      forgetPasswordPage,
      page,
    }) => {
      // Mock the API response for non-existent user
      await page.route('**/users/forgot-password/', async route => {
        await route.fulfill({
          status: 404,
          contentType: 'application/json',
          body: JSON.stringify({
            error: TestConfig.messages.userNotFound,
          }),
        });
      });

      // Use the resetPassword method and get the result
      const resetResult = await forgetPasswordPage.resetPassword(
        TestConfig.forgetPassword.invalid.email,
      );

      // Verify we're still on the forget password page
      await expect(page).toHaveURL(/\/forget-password/);

      // Check for error toast
      if (resetResult.hasErrorToast && resetResult.errorToastText) {
        expect(resetResult.errorToastText).toContain(
          TestConfig.messages.userNotFound,
        );
      }
    });
  });

  test.describe('Navigation', () => {
    test('should navigate to signup page', async ({
      forgetPasswordPage,
      page,
    }) => {
      await forgetPasswordPage.goToSignUp();
      await expect(page).toHaveURL(/\/register/);
    });
  });

  test.describe('Security Tests', () => {
    test('should handle XSS attempts in email field', async ({
      forgetPasswordPage,
    }) => {
      // Try to inject XSS payload
      await forgetPasswordPage.fillEmail(TestConfig.securityPayloads.xss);

      // The form should sanitize inputs or at least not execute scripts
      // Should not crash the application
      await expect(forgetPasswordPage.emailInput).toBeVisible();
    });

    test('should handle extremely long inputs', async ({
      forgetPasswordPage,
    }) => {
      // Try very long input to test against buffer overflow
      await forgetPasswordPage.fillEmail(
        `long-email-${TestConfig.securityPayloads.longInput.substring(0, 50)}@example.com`,
      );

      // Form should still be functional after attempt
      await expect(forgetPasswordPage.emailInput).toBeVisible();
    });
  });
});
