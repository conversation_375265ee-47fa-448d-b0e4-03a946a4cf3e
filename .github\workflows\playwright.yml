name: Playwright E2E Tests

on:
  push:
    branches: [ main, master, dev, test ]
  pull_request:
    branches: [ main, master, dev, test ]

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        # Run tests on multiple browsers
        browser: [chromium, firefox, webkit]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: 'npm'

    - name: Install dependencies
      run: npm ci --legacy-peer-deps

    - name: Install Angular CLI
      run: npm install -g @angular/cli@18.2.1

    - name: Install Playwright Browsers
      run: npx playwright install --with-deps ${{ matrix.browser }}

    - name: Build Angular application
      run: |
        if [[ "${{ github.ref_name }}" == "main" || "${{ github.ref_name }}" == "master" ]]; then
          ng build --configuration production
        elif [[ "${{ github.ref_name }}" == "test" ]]; then
          ng build --configuration test
        else
          ng build --configuration development
        fi

    - name: Start Angular dev server
      run: |
        ng serve --host 0.0.0.0 --port 4200 &
        # Wait for server to be ready
        npx wait-on http://localhost:4200 --timeout 60000
      if: github.ref_name != 'main' && github.ref_name != 'master' && github.ref_name != 'test'

    - name: Run Playwright tests
      run: npx playwright test --project=${{ matrix.browser }}
      env:
        # Set base URL based on branch
        PLAYWRIGHT_BASE_URL: ${{ github.ref_name == 'main' || github.ref_name == 'master' ? 'https://platform.aicuflow.com' : github.ref_name == 'test' ? 'https://platform.test.aicuflow.com' : 'http://localhost:4200' }}

    - name: Upload Playwright Report
      uses: actions/upload-artifact@v4
      if: ${{ !cancelled() }}
      with:
        name: playwright-report-${{ matrix.browser }}
        path: playwright-report/
        retention-days: 30

    - name: Upload Test Results
      uses: actions/upload-artifact@v4
      if: ${{ !cancelled() }}
      with:
        name: test-results-${{ matrix.browser }}
        path: test-results/
        retention-days: 30

  # Merge reports from all browsers into a single report
  merge-reports:
    if: ${{ !cancelled() }}
    needs: [test]
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: 'npm'

    - name: Install dependencies
      run: npm ci --legacy-peer-deps

    - name: Download all reports
      uses: actions/download-artifact@v4
      with:
        path: all-blob-reports
        pattern: test-results-*
        merge-multiple: true

    - name: Merge into HTML Report
      run: npx playwright merge-reports --reporter html ./all-blob-reports

    - name: Upload Merged HTML Report
      uses: actions/upload-artifact@v4
      with:
        name: playwright-report-merged
        path: playwright-report/
        retention-days: 30

    - name: Comment PR with report link
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          // Create a comment with the report link
          const comment = `## 🎭 Playwright Test Results
          
          The E2E tests have completed. You can download the test report from the artifacts section of this workflow run.
          
          **Artifacts available:**
          - \`playwright-report-merged\` - Combined HTML report from all browsers
          - \`playwright-report-chromium\` - Chromium-specific report
          - \`playwright-report-firefox\` - Firefox-specific report  
          - \`playwright-report-webkit\` - WebKit-specific report
          
          To view the HTML report locally:
          1. Download the \`playwright-report-merged\` artifact
          2. Extract the zip file
          3. Run \`npx playwright show-report <extracted-folder>\`
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });
