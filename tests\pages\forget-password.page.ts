import { Page, Locator } from '@playwright/test';

export class ForgetPasswordPage {
  // Locators
  readonly emailInput: Locator;
  readonly resetButton: Locator;
  readonly signUpLink: Locator;
  readonly welcomeText: Locator;
  readonly welcomeImage: Locator;
  readonly logoImage: Locator;
  readonly emailErrorMessage: Locator;
  readonly successToast: Locator;
  readonly errorToast: Locator;
  readonly forgotPasswordTitle: Locator;

  constructor(private page: Page) {
    this.emailInput = page.locator('input[formControlName="email"]');
    this.resetButton = page.locator('button:has-text("Reset")');
    this.signUpLink = page.locator('a', { hasText: 'Sign Up' });
    this.welcomeText = page.locator('h1.welcome', { hasText: 'Welcome' });
    this.welcomeImage = page.locator('img[src*="assets/Welcome.png"]');
    this.logoImage = page.locator('img[src*="assets/Logo.png"]');
    this.emailErrorMessage = page.locator(
      'text=Please enter a valid email address',
    );
    this.successToast = page.locator('.toast-success');
    this.errorToast = page.locator('.toast-error');
    this.forgotPasswordTitle = page.locator('h3', {
      hasText: 'Forgot Password?',
    });
  }

  /**
   * Navigate to the forget password page
   */
  async goto() {
    try {
      // Navigate to the forget password page with a longer timeout
      await this.page.goto('/auth/forget-password', { timeout: 30000 });

      // Wait for the page to be fully loaded
      await this.page.waitForLoadState('domcontentloaded', { timeout: 30000 });
      await this.page.waitForLoadState('networkidle', { timeout: 30000 });

      // Wait a bit more to ensure everything is loaded
      await this.page.waitForTimeout(1000);
    } catch (e) {
      // Error navigating to forget password page
      throw e;
    }
  }

  /**
   * Fill the email field
   */
  async fillEmail(email: string) {
    await this.emailInput.fill(email);
  }

  /**
   * Click the reset button
   */
  async clickReset() {
    try {
      // Click the reset button with a longer timeout
      await this.resetButton.click({ timeout: 10000 });

      // Wait for navigation or response
      await this.page.waitForTimeout(2000); // Wait a bit for any response

      // Wait for any network requests to complete
      await this.page.waitForLoadState('networkidle', { timeout: 10000 });
    } catch (e) {
      // Error clicking reset button
      throw e; // Re-throw to allow test to fail
    }
  }

  /**
   * Complete the reset password process with provided email
   * Returns the current state without assertions
   */
  async resetPassword(email: string) {
    try {
      // Fill the email
      await this.fillEmail(email);

      // Click reset
      await this.clickReset();

      // Return current state without assertions
      return {
        url: this.page.url(),
        hasSuccessToast: await this.successToast.isVisible(),
        hasErrorToast: await this.errorToast.isVisible(),
        successToastText: await this.getSuccessToastText(),
        errorToastText: await this.getErrorToastText(),
      };
    } catch (e) {
      // Error during reset password process
      return {
        url: this.page.url(),
        hasSuccessToast: false,
        hasErrorToast: false,
        successToastText: null,
        errorToastText: null,
        error: e,
      };
    }
  }

  /**
   * Navigate to the signup page
   */
  async goToSignUp() {
    await this.signUpLink.click();
    await this.page.waitForURL(/\/register/, { timeout: 10000 });
  }

  /**
   * Check if reset button is disabled
   */
  async isResetButtonDisabled() {
    return this.resetButton.isDisabled();
  }

  /**
   * Get the current URL
   */
  async getCurrentUrl() {
    return this.page.url();
  }

  /**
   * Check if email validation error is visible
   */
  async isEmailErrorVisible() {
    return this.emailErrorMessage.isVisible();
  }

  /**
   * Get text from success toast if visible
   */
  async getSuccessToastText() {
    if (await this.successToast.isVisible()) {
      return await this.successToast.textContent();
    }
    return null;
  }

  /**
   * Get text from error toast if visible
   */
  async getErrorToastText() {
    try {
      const errorToasts = this.page.locator('.toast-error');
      const count = await errorToasts.count();

      if (count > 0) {
        // Get text from the first error toast
        return await errorToasts.first().textContent();
      }
    } catch (e) {
      // Error getting error toast text
    }
    return null;
  }

  /**
   * Get visibility status of UI elements on the forget password page
   * @returns Object containing visibility status of all UI elements
   */
  async getUIElementsVisibility() {
    try {
      // Use longer timeouts for visibility checks
      const timeout = { timeout: 10000 };

      // Collect the visibility status of all elements
      const currentUrl = this.page.url();
      const isOnForgetPasswordPage = /\/forget-password/i.test(currentUrl);

      return {
        currentUrl,
        isOnForgetPasswordPage,
        formElements: {
          emailInputVisible: await this.emailInput.isVisible(timeout),
          resetButtonVisible: await this.resetButton.isVisible(timeout),
        },
        navigationElements: {
          signUpLinkVisible: await this.signUpLink.isVisible(timeout),
        },
        uiElements: {
          welcomeTextVisible: await this.welcomeText.isVisible(timeout),
          welcomeImageVisible: await this.welcomeImage.isVisible(timeout),
          logoImageVisible: await this.logoImage.isVisible(timeout),
          forgotPasswordTitleVisible:
            await this.forgotPasswordTitle.isVisible(timeout),
        },
      };
    } catch (e: unknown) {
      // Error checking UI elements visibility
      const errorMessage = e instanceof Error ? e.message : String(e);

      return {
        error: errorMessage,
        currentUrl: 'unknown',
        isOnForgetPasswordPage: false,
        formElements: {
          emailInputVisible: false,
          resetButtonVisible: false,
        },
        navigationElements: {
          signUpLinkVisible: false,
        },
        uiElements: {
          welcomeTextVisible: false,
          welcomeImageVisible: false,
          logoImageVisible: false,
          forgotPasswordTitleVisible: false,
        },
      };
    }
  }
}
