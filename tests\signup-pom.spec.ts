import { test, expect } from './fixtures/base-fixture';
import { TestConfig } from './config/test-config';

test.describe('Signup Page', () => {
  test.beforeEach(async ({ signupPage }) => {
    // Navigate to signup page
    await signupPage.goto();
  });

  test('signup page should load correctly', async ({ signupPage }) => {
    // Get the visibility status of all UI elements
    const visibility = await signupPage.getUIElementsVisibility();

    // Verify we're on the signup page
    expect(visibility.isOnSignupPage).toBeTruthy();

    // Verify form elements are visible
    expect(visibility.formElements.firstNameInputVisible).toBeTruthy();
    expect(visibility.formElements.lastNameInputVisible).toBeTruthy();
    expect(visibility.formElements.emailInputVisible).toBeTruthy();
    expect(visibility.formElements.passwordInputVisible).toBeTruthy();
    expect(visibility.formElements.confirmPasswordInputVisible).toBeTruthy();
    expect(visibility.formElements.signupButtonVisible).toBeTruthy();

    // Verify navigation elements are visible
    expect(visibility.navigationElements.loginLinkVisible).toBeTruthy();

    // Verify UI elements are visible
    expect(visibility.uiElements.welcomeTextVisible).toBeTruthy();
    expect(visibility.uiElements.welcomeImageVisible).toBeTruthy();
    expect(visibility.uiElements.logoImageVisible).toBeTruthy();
  });

  test.describe('Form Validation', () => {
    test('should disable signup button for empty or invalid inputs', async ({
      signupPage,
    }) => {
      // Initially button should be disabled
      expect(await signupPage.isSignupButtonDisabled()).toBeTruthy();

      // Fill only first name
      await signupPage.firstNameInput.fill('Test');
      expect(await signupPage.isSignupButtonDisabled()).toBeTruthy();

      // Fill only last name
      await signupPage.firstNameInput.clear();
      await signupPage.lastNameInput.fill('User');
      expect(await signupPage.isSignupButtonDisabled()).toBeTruthy();

      // Fill only email
      await signupPage.lastNameInput.clear();
      await signupPage.emailInput.fill('<EMAIL>');
      expect(await signupPage.isSignupButtonDisabled()).toBeTruthy();

      // Fill all fields with valid values
      await signupPage.firstNameInput.fill('Test');
      await signupPage.lastNameInput.fill('User');
      await signupPage.passwordInput.fill('Password123!');
      await signupPage.confirmPasswordInput.fill('Password123!');
      expect(await signupPage.isSignupButtonDisabled()).toBeFalsy();
    });

    test('should show validation errors for invalid email and password formats', async ({
      signupPage,
    }) => {
      const { email, password, confirmPassword } = TestConfig.signup.malformed;

      // Test invalid email format
      await signupPage.emailInput.fill(email);
      await signupPage.emailInput.blur();
      expect(await signupPage.isEmailErrorVisible()).toBeTruthy();

      // Test invalid password (too short)
      await signupPage.passwordInput.fill(password);
      await signupPage.passwordInput.blur();
      expect(await signupPage.isPasswordErrorVisible()).toBeTruthy();

      // Test password mismatch
      await signupPage.confirmPasswordInput.fill(confirmPassword);
      await signupPage.confirmPasswordInput.blur();
      expect(await signupPage.isPasswordMatchErrorVisible()).toBeTruthy();

      // Button should be disabled with invalid inputs
      expect(await signupPage.isSignupButtonDisabled()).toBeTruthy();
    });

    test('password visibility toggle should work', async ({ signupPage }) => {
      // Fill password field
      await signupPage.passwordInput.fill('Password123!');

      // Check initial type (should be password/hidden)
      expect(await signupPage.passwordInput.getAttribute('type')).toBe(
        'password',
      );

      // Toggle visibility
      await signupPage.togglePasswordVisibility();

      // Check if type changed to text
      expect(await signupPage.passwordInput.getAttribute('type')).toBe('text');

      // Toggle back
      await signupPage.togglePasswordVisibility();

      // Check if type changed back to password
      expect(await signupPage.passwordInput.getAttribute('type')).toBe(
        'password',
      );
    });
  });

  test.describe('Registration', () => {
    test('should register a new user with valid information', async ({
      signupPage,
    }) => {
      const { firstName, lastName, email, password, confirmPassword } =
        TestConfig.signup.valid;

      // Use the signup method and get the result
      const signupResult = await signupPage.signup(
        firstName,
        lastName,
        email,
        password,
        confirmPassword,
      );

      // Check for success toast or redirection
      if (signupResult.hasSuccessToast && signupResult.successToastText) {
        expect(signupResult.successToastText).toContain(
          TestConfig.messages.signupSuccess,
        );
      }

      // Check if we've navigated away from the register page
      const currentUrl = signupResult.url;
      expect(currentUrl.includes('/register')).toBeFalsy();
    });

    test('should show error for duplicate email', async ({
      signupPage,
      page,
    }) => {
      const { firstName, lastName, email, password, confirmPassword } =
        TestConfig.signup.invalid;

      // Fill the form with data that has a duplicate email
      await signupPage.fillSignupForm(
        firstName,
        lastName,
        email,
        password,
        confirmPassword,
      );

      // Submit the form
      await signupPage.clickSignup();

      // Verify we're still on the signup page
      await expect(page).toHaveURL(/\/register/);

      // Check for error toast
      const errorToastText = await signupPage.getErrorToastText();
      if (errorToastText) {
        expect(errorToastText).toBeTruthy();
      }
    });
  });

  test.describe('Navigation', () => {
    test('should navigate to login page', async ({ signupPage, page }) => {
      await signupPage.goToLogin();
      await expect(page).toHaveURL(/\/login/);
    });
  });

  test.describe('Security Tests', () => {
    test('should handle XSS attempts in input fields', async ({
      signupPage,
    }) => {
      // Try to inject XSS payload
      await signupPage.firstNameInput.fill(TestConfig.securityPayloads.xss);
      await signupPage.lastNameInput.fill(TestConfig.securityPayloads.xss);
      await signupPage.emailInput.fill('<EMAIL>'); // Valid email to enable the button
      await signupPage.passwordInput.fill('Password123!');
      await signupPage.confirmPasswordInput.fill('Password123!');

      // The form should sanitize inputs or at least not execute scripts
      // Should not crash the application
      await expect(signupPage.firstNameInput).toBeVisible();
    });

    test('should handle extremely long inputs', async ({ signupPage }) => {
      // Try very long inputs to test against buffer overflow
      const longInput = TestConfig.securityPayloads.longInput.substring(0, 50);

      await signupPage.firstNameInput.fill(longInput);
      await signupPage.lastNameInput.fill(longInput);
      await signupPage.emailInput.fill(
        `long-email-${longInput.substring(0, 10)}@example.com`,
      );
      await signupPage.passwordInput.fill('Password123!');
      await signupPage.confirmPasswordInput.fill('Password123!');

      // Form should still be functional after attempt
      await expect(signupPage.firstNameInput).toBeVisible();
    });
  });
});
