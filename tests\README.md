# E2E Testing with <PERSON><PERSON>

This directory contains end-to-end tests for the AICU frontend application using Playwright.

## Structure

- `tests/` - Contains all test files
  - `pages/` - Page Object Models for different pages
  - `fixtures/` - Test fixtures and utilities
  - `config/` - Test configuration
  - `*.spec.ts` - Test files

## Page Object Model (POM)

We use the Page Object Model pattern to organize our tests. Each page in the application has a corresponding class that encapsulates the page's functionality.

Example:

```typescript
// LoginPage class encapsulates all interactions with the login page
const loginPage = new LoginPage(page);
await loginPage.goto();
await loginPage.login('<EMAIL>', 'password');
```

## Running Tests

You can run tests using either npm scripts or direct Playwright commands.

### Using npm scripts

```bash
# Run all tests
npm run test:e2e

# Run specific test file
npm run test:e2e:login

# Run tests in headed mode
npm run test:e2e:headed

# Run tests in debug mode
npm run test:e2e:debug

# View test report
npm run test:e2e:report
```

### Using Playwright commands

```bash
# Run all tests
npx playwright test

# Run specific test file
npx playwright test tests/login-pom.spec.ts

# Run tests in headed mode
npx playwright test --headed

# Run tests in debug mode
npx playwright test --debug

# Run tests in UI mode
npx playwright test --ui

# View test report
npx playwright show-report
```

## Environment Configuration

Tests can be configured to run against different environments by modifying the `baseUrl` in `tests/config/test-config.ts`:

```typescript
export const TestConfig = {
  // For local development
  baseUrl: 'http://localhost:4200',

  // For dev environment
  // baseUrl: 'http://platform.dev.aicuflow.com',

  // For beta environment
  // baseUrl: 'http://platform.beta.aicuflow.com',

  // For production environment
  // baseUrl: 'https://platform.aicuflow.com',

  // Rest of the configuration...
};
```

The `baseUrl` is also referenced in `playwright.config.ts` to set the base URL for all tests.

## Test Coverage

Current test coverage includes:

- **Authentication**

  - Login functionality (login-pom.spec.ts)
  - Signup functionality (signup-pom.spec.ts)
  - Forget password functionality (forget-password-pom.spec.ts)
  - New password functionality (new-password-pom.spec.ts)
  - Form validation for all authentication forms
  - Session management
  - Navigation between auth pages

- **Security Testing**

  - XSS protection
  - SQL injection protection
  - Input validation

- **Performance Testing**
  - Response time measurements for critical operations

## Adding New Tests

1. Create a Page Object Model for the page you want to test
2. Add test configuration if needed
3. Create a test file following the existing patterns
4. Run the tests to verify they work

## Troubleshooting

- **Tests failing with timeout errors**: Increase timeout in the test or check if selectors are correct
- **Multiple elements found**: Make selectors more specific
- **Element not found**: Check if the element exists in the page or if it's in a different state
- **Tests fail against hosted environments**: Verify the baseUrl configuration and ensure the application is accessible

## Future Improvements

- Add more page objects for other parts of the application
- Add visual regression testing
- Add accessibility testing
- Implement combined flow tests for complete user journeys
- Add data-driven testing for more comprehensive coverage
