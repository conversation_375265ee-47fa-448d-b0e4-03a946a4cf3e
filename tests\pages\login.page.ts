import { Page, Locator } from '@playwright/test';

export class LoginPage {
  // Locators
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly loginButton: Locator;
  readonly signUpLink: Locator;
  readonly forgotPasswordLink: Locator;
  readonly welcomeText: Locator;
  readonly welcomeImage: Locator;
  readonly logoImage: Locator;
  readonly emailErrorMessage: Locator;
  readonly passwordErrorMessage: Locator;
  readonly successToast: Locator;
  readonly errorToast: Locator;

  constructor(private page: Page) {
    this.emailInput = page.locator('input[formControlName="email"]');
    this.passwordInput = page.locator('input[formControlName="password"]');
    this.loginButton = page.locator('button:has-text("Login")');
    this.signUpLink = page.locator('a', { hasText: 'Sign Up' });
    this.forgotPasswordLink = page.locator('a', {
      hasText: 'Forgot your Password',
    });
    this.welcomeText = page.locator('h1.welcome', { hasText: 'Welcome' });
    this.welcomeImage = page.locator('img[src*="assets/Welcome.png"]');
    this.logoImage = page.locator('img[src*="assets/Logo.png"]');
    this.emailErrorMessage = page.locator(
      'text=Please enter a valid email address',
    );
    this.passwordErrorMessage = page.locator(
      'text=Minimum 8 characters are required',
    );
    this.successToast = page.locator('.toast-success');
    this.errorToast = page.locator('.toast-error');
  }

  /**
   * Navigate to the login page
   */
  async goto() {
    try {
      // Navigate to the login page with a longer timeout
      await this.page.goto('/', { timeout: 30000 });

      // Wait for the page to be fully loaded
      await this.page.waitForLoadState('domcontentloaded', { timeout: 30000 });
      await this.page.waitForLoadState('networkidle', { timeout: 30000 });

      // Wait a bit more to ensure everything is loaded
      await this.page.waitForTimeout(1000);
    } catch (e) {
      // Error navigating to login page
      throw e;
    }
  }

  /**
   * Fill the login form with email and password
   */
  async fillLoginForm(email: string, password: string) {
    await this.emailInput.fill(email);
    await this.passwordInput.fill(password);
  }

  /**
   * Click the login button
   */
  async clickLogin() {
    try {
      // Click the login button with a longer timeout
      await this.loginButton.click({ timeout: 10000 });

      // Wait for navigation or response
      await this.page.waitForTimeout(2000); // Wait a bit for any response

      // Wait for any network requests to complete
      await this.page.waitForLoadState('networkidle', { timeout: 10000 });
    } catch (e) {
      // Error clicking login button
      throw e;
    }
  }

  /**
   * Complete the login process with provided credentials
   * Returns the current state without assertions
   */
  async login(email: string, password: string) {
    try {
      // Fill the form
      await this.fillLoginForm(email, password);

      // Click login
      await this.clickLogin();

      // Return current state without assertions
      return {
        url: this.page.url(),
        hasSuccessToast: await this.successToast.isVisible(),
        hasErrorToast: await this.errorToast.isVisible(),
        errorToastText: await this.getErrorToastText(),
      };
    } catch (e) {
      // Error during login process
      return {
        url: this.page.url(),
        hasSuccessToast: false,
        hasErrorToast: false,
        errorToastText: null,
        error: e,
      };
    }
  }

  /**
   * Navigate to the signup page
   */
  async goToSignUp() {
    await this.signUpLink.click();
    await this.page.waitForURL(/\/register/, { timeout: 10000 });
  }

  /**
   * Navigate to the forgot password page
   */
  async goToForgotPassword() {
    await this.forgotPasswordLink.click();
    await this.page.waitForURL(/\/forget-password/, { timeout: 10000 });
  }

  /**
   * Check if login button is disabled
   */
  async isLoginButtonDisabled() {
    return this.loginButton.isDisabled();
  }

  /**
   * Get the current URL
   */
  async getCurrentUrl() {
    return this.page.url();
  }

  /**
   * Check if we're on the dashboard page
   */
  async isOnDashboard() {
    return this.page.url().includes('/dashboard/projects');
  }

  /**
   * Clear browser storage (localStorage and sessionStorage)
   */
  async clearStorage() {
    await this.page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
  }

  /**
   * Check if email validation error is visible
   */
  async isEmailErrorVisible() {
    return this.emailErrorMessage.isVisible();
  }

  /**
   * Check if password validation error is visible
   */
  async isPasswordErrorVisible() {
    return this.passwordErrorMessage.isVisible();
  }

  /**
   * Get text from success toast if visible
   */
  async getSuccessToastText() {
    if (await this.successToast.isVisible()) {
      return await this.successToast.textContent();
    }
    return null;
  }

  /**
   * Get text from error toast if visible
   */
  async getErrorToastText() {
    try {
      const errorToasts = this.page.locator('.toast-error');
      const count = await errorToasts.count();

      if (count > 0) {
        // Get text from the first error toast
        return await errorToasts.first().textContent();
      }
    } catch (e) {
      // Error getting error toast text
    }
    return null;
  }

  /**
   * Check if specific error toast message is visible
   */
  async isSpecificErrorToastVisible(errorText: string) {
    const errorToast = this.page.locator(
      `.toast-error:has-text("${errorText}")`,
    );
    return errorToast.isVisible();
  }

  /**
   * Get visibility status of UI elements on the login page
   * @returns Object containing visibility status of all UI elements
   */
  async getUIElementsVisibility() {
    try {
      // Use longer timeouts for visibility checks
      const timeout = { timeout: 10000 };

      // Collect the visibility status of all elements
      const currentUrl = this.page.url();
      const isOnLoginPage = /\/login$|\/$/i.test(currentUrl);

      return {
        currentUrl,
        isOnLoginPage,
        formElements: {
          emailInputVisible: await this.emailInput.isVisible(timeout),
          passwordInputVisible: await this.passwordInput.isVisible(timeout),
          loginButtonVisible: await this.loginButton.isVisible(timeout),
        },
        navigationElements: {
          signUpLinkVisible: await this.signUpLink.isVisible(timeout),
          forgotPasswordLinkVisible:
            await this.forgotPasswordLink.isVisible(timeout),
        },
        uiElements: {
          welcomeTextVisible: await this.welcomeText.isVisible(timeout),
          welcomeImageVisible: await this.welcomeImage.isVisible(timeout),
          logoImageVisible: await this.logoImage.isVisible(timeout),
        },
      };
    } catch (e: unknown) {
      // Error checking UI elements visibility
      const errorMessage = e instanceof Error ? e.message : String(e);

      return {
        error: errorMessage,
        currentUrl: 'unknown',
        isOnLoginPage: false,
        formElements: {
          emailInputVisible: false,
          passwordInputVisible: false,
          loginButtonVisible: false,
        },
        navigationElements: {
          signUpLinkVisible: false,
          forgotPasswordLinkVisible: false,
        },
        uiElements: {
          welcomeTextVisible: false,
          welcomeImageVisible: false,
          logoImageVisible: false,
        },
      };
    }
  }
}
