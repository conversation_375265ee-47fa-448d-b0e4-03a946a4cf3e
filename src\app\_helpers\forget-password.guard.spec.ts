import { TestBed } from '@angular/core/testing';
import { CanActivateFn } from '@angular/router';

import { forgetPasswordGuard } from './forget-password.guard';

describe('forgetPasswordGuard', () => {
  const executeGuard: CanActivateFn = (...guardParameters) =>
    TestBed.runInInjectionContext(() =>
      forgetPasswordGuard(...guardParameters),
    );

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeGuard).toBeTruthy();
  });
});
